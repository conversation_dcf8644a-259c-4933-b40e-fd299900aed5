{"cells": [{"cell_type": "markdown", "id": "ba9301f2", "metadata": {}, "source": ["### Abstraction"]}, {"cell_type": "markdown", "id": "998efb25", "metadata": {}, "source": ["Abstraction is the concept of hiding complex implementation details ans showing only the necessary featurea of object. This helps in reducing programming complexity and effort."]}, {"cell_type": "code", "execution_count": 1, "id": "2fe83700", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The car engine is started.\n"]}], "source": ["from abc import ABC,abstractmethod\n", "\n", "## Abstract class \n", "class Vechile(ABC):\n", "    def drive(self):\n", "        print(\"The Vechile is used for driving.\")\n", "    \n", "    @abstractmethod\n", "    def start_engine(self):\n", "        pass\n", "\n", "class Car(Vechile):\n", "    def start_engine(self):\n", "        print(\"The car engine is started.\")\n", "\n", "def operate_vechile(Vechile):\n", "    Vechile.start_engine()\n", "\n", "\n", "car = Car()\n", "operate_vechile(car)"]}, {"cell_type": "code", "execution_count": null, "id": "940bc544", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}