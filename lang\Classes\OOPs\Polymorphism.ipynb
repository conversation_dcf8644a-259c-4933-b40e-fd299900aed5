{"cells": [{"cell_type": "markdown", "id": "6fdefe7d", "metadata": {}, "source": ["### Polymorphism"]}, {"cell_type": "markdown", "id": "f3e4175a", "metadata": {}, "source": ["#### It Allow obeject of diffent class to be treated as object of common super class "]}, {"cell_type": "markdown", "id": "4166484a", "metadata": {}, "source": ["### Method Overriding\n", "MEthod overriding allows a child class to be provided a specific implementation of a method thats is already provided by one of its parent classes."]}, {"cell_type": "code", "execution_count": null, "id": "70431d2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dog barking\n", "Cat meowing\n", "Dog barking\n", "Cat meowing\n"]}], "source": ["## Base Class\n", "class Animal:\n", "    def speak(self):\n", "        return \"Animal speaking\"\n", "        \n", "## Derived Class 1\n", "class Dog(Animal):\n", "    def speak(self):            #Method Overriding\n", "        return \"Dog barking\"\n", "        \n", "## Derived Class 2\n", "class Cat(Animal):\n", "    def speak(self):\n", "        return \"Cat meowing\"    \n", "\n", "##function with demonstrates polymorphism\n", "def animal_speak(animal):\n", "    return(animal.speak())\n", "\n", "\n", "## Creating objects\n", "dog = Dog()\n", "cat = Cat()\n", "\n", "\n", "\n", "## Calling the speak method\n", "print(dog.speak())  # Output: Dog barking\n", "print(cat.speak())  # Output: <PERSON> meowing\n", "print(animal_speak(dog))  # Output: Dog barking\n", "print(animal_speak(cat))  # Output: Cat meowing\n"]}, {"cell_type": "code", "execution_count": 9, "id": "b9c95999", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n", "28.26\n"]}], "source": ["### Polymorphism with a function and objects\n", "## base class\n", "class Shape:\n", "    def area(self):\n", "        return\"Area of Shape\"\n", "\n", "# Derived class 1\n", "class Rectangle(Shape):\n", "    def __init__(self,width,height):\n", "        self.width = width\n", "        self.height = height\n", "    def area(self):\n", "        return self.width * self.height\n", "\n", "# Derived class 2\n", "class Circle(Shape ):\n", "    def __init__(self,radius):\n", "        self.radius = radius\n", "    def area(self):\n", "        return 3.14 * self.radius ** 2\n", "\n", "# Function that demonstrate polymophism\n", "def print_area(shape):\n", "    print(shape.area())\n", "\n", "rectangle = Rectangle(4,5)\n", "circle = Circle(3)\n", "\n", "print_area(rectangle) # Output: 20\n", "print_area(circle) # Output: 28.26"]}, {"cell_type": "markdown", "id": "b79ccc5b", "metadata": {}, "source": ["### polymorphism with abstract base class\n", "Abstract base classes (ABCs) are used to define common method for a grp of releted objects. They can enforce that derived classes implement particular methods, promoting consistancy across diffent implementation "]}, {"cell_type": "code", "execution_count": 22, "id": "e75a93ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Bike engine started'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["from abc import ABC, abstractmethod \n", "\n", "## Define an abstract class \n", "class Vehicle(ABC):\n", "    @abstractmethod \n", "    def start_engine(self):\n", "        pass\n", "\n", "## Derived class 1\n", "class Car(Vehicle):\n", "    def start_engine(self):\n", "        return \"Car engine started\"\n", "\n", "## Derived class 2\n", "class Bike(Vehicle):\n", "    def start_engine(self):\n", "        return \"Bike engine started\"\n", "\n", "def start_vehicle(vehicle):\n", "    return vehicle.start_engine()\n", "\n", "\n", "car = Car()\n", "bike = Bike()\n", "\n", "start_vehicle(car) # Output: Car engine started\n", "start_vehicle(bike) # Output: Bike engine started"]}, {"cell_type": "code", "execution_count": null, "id": "30c8e24f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e4a757af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d29cf741", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed086a26", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "42febde0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ead83d8c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2a6e4c14", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1118e5fc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d480bfa1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22dfaf2e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3110e064", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Car engine started\n", "Bike engine started\n"]}], "source": ["\n"]}, {"cell_type": "code", "execution_count": 30, "id": "dbec61bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bike engine started\n"]}], "source": ["from abc import ABC, abstractmethod \n", "\n", "## Define an abstract class \n", "class Vehicle(ABC):\n", "    @abstractmethod \n", "    def start_engine(self):\n", "        pass\n", "\n", "## Derived class 1\n", "class Car(Vehicle):\n", "    def start_engine(self):\n", "        return \"Car engine started\"\n", "\n", "## Derived class 2\n", "class Bike(Vehicle):\n", "    def start_engine(self):\n", "        return \"Bike engine started\"\n", "\n", "def start_vehicle(vehicle):\n", "    return vehicle.start_engine()\n", "\n", "\n", "car = Car()\n", "bike = Bike()\n", "\n", "start_vehicle(car) # Output: Car engine started\n", "print(start_vehicle(bike)) # Output: Bike engine started\n"]}, {"cell_type": "markdown", "id": "4e5f04c9", "metadata": {}, "source": ["### Conclusion \n", "polymorphism is a powerful feature of oop that allows for flexibility and integration in code design.It enables a single function to handle object of different classes in a uniform way, making code more readable and maintainable."]}, {"cell_type": "markdown", "id": "899ab263", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}