{"cells": [{"cell_type": "code", "execution_count": 1, "id": "76087f95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["variable not been assignend\n"]}], "source": ["##exception try and except\n", "try:\n", "    a=b\n", "except:\n", "    print(\"variable not been assignend\")"]}, {"cell_type": "code", "execution_count": 2, "id": "02b46d51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name 'b' is not defined\n"]}], "source": ["try:\n", "    a=b\n", "except NameError as ex:\n", "    print(ex)"]}, {"cell_type": "code", "execution_count": null, "id": "8035b9fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name 'b' is not defined\n"]}], "source": ["try:\n", "    result=1/2\n", "    a=b\n", "except Exception as ex: ## parent class of exception\n", "    print(ex)"]}, {"cell_type": "code", "execution_count": null, "id": "515a0873", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}