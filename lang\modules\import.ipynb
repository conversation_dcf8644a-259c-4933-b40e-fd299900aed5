{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1693f885", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from package.maths import addition\n", "addition(2,3)"]}, {"cell_type": "code", "execution_count": null, "id": "504bbc7d", "metadata": {}, "outputs": [], "source": ["from package import maths"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}