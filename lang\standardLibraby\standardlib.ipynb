{"cells": [{"cell_type": "markdown", "id": "3b03174b", "metadata": {}, "source": ["## Standard Library Overvier"]}, {"cell_type": "code", "execution_count": 1, "id": "617b634d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["array('i', [1, 2, 3, 4])\n"]}], "source": ["import array\n", "arr = array.array('i',[1,2,3,4])\n", "print(arr)"]}, {"cell_type": "code", "execution_count": 2, "id": "b0914b8a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.0\n", "3.141592653589793\n"]}], "source": ["import math\n", "print(math.sqrt(16))\n", "print(math.pi)"]}, {"cell_type": "code", "execution_count": 3, "id": "06854af6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["d:\\GenAi\\langchain\\standardLibraby\n"]}], "source": ["### file and directory access\n", "import os\n", "print(os.getcwd())\n"]}, {"cell_type": "code", "execution_count": 7, "id": "94a879dc", "metadata": {}, "outputs": [], "source": ["os.mkdir(\"test_dir\")"]}, {"cell_type": "code", "execution_count": 6, "id": "dd13b30e", "metadata": {}, "outputs": [], "source": ["with open('myfile','w') as  f:\n", "    pass"]}, {"cell_type": "code", "execution_count": 8, "id": "9486411c", "metadata": {}, "outputs": [], "source": ["##high level operation on file or collection of files"]}, {"cell_type": "code", "execution_count": 9, "id": "6f7b5697", "metadata": {}, "outputs": [{"data": {"text/plain": ["'desition.txt'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import shutil\n", "shutil.copyfile('source.txt','desition.txt')"]}, {"cell_type": "code", "execution_count": 12, "id": "e298f03f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"name\": \"<PERSON><PERSON><PERSON><PERSON>\", \"age\": 23}\n", "<class 'str'>\n", "{'name': '<PERSON><PERSON><PERSON><PERSON>', 'age': 23}\n", "<class 'dict'>\n"]}], "source": ["## Data Serialization\n", "import json \n", "data = {\"name\":\"<PERSON><PERSON><PERSON><PERSON>\", \"age\":23}\n", "\n", "json_str = json.dumps(data)\n", "print(json_str)\n", "print(type(json_str))\n", "\n", "parsed_data = json.loads(json_str)\n", "print(parsed_data)\n", "print(type(parsed_data))"]}, {"cell_type": "code", "execution_count": 1, "id": "029a4440", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['name', 'age']\n", "['kaustubh', '24']\n"]}], "source": ["#csv \n", "import csv \n", "with open (\"example.csv\",\"w\",newline=\"\")as file:\n", "  writer = csv.writer(file)\n", "  writer.writerow([\"name\",\"age\"])\n", "  writer.writerow([\"kaustubh\",24])\n", "\n", "with open (\"example.csv\",'r') as file:\n", "  reader = csv.reader(file)\n", "  for row in reader:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 8, "id": "77361e5c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-25 18:13:46.215700\n", "2025-06-24 18:13:46.215700\n"]}], "source": ["## datetime \n", "from datetime import datetime, timedelta\n", "\n", "noww = datetime.now()\n", "print(noww)\n", "\n", "\n", "yesterday = noww- <PERSON><PERSON><PERSON>(days=1)\n", "print(yesterday)"]}, {"cell_type": "code", "execution_count": 1, "id": "4a3ce212", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1750856502.5843341\n", "1750856504.5849137\n"]}], "source": ["## time \n", "import time \n", "print(time.time())\n", "time.sleep(2)\n", "print(time.time())"]}, {"cell_type": "code", "execution_count": null, "id": "3b8ee74e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["123\n"]}], "source": ["## Regular Expression\n", "import re \n", "\n", "pattern = r\"\\d+\"  ##Check for digit when it match it stops there\n", "text  = \"There are 123 apples\"\n", "match = re.search(pattern,text)\n", "print(match.group())"]}, {"cell_type": "code", "execution_count": null, "id": "a4831974", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}