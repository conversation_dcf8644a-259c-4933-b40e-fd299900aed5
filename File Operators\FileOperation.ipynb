{"cells": [{"cell_type": "markdown", "id": "f14a5870", "metadata": {}, "source": ["## File opeartor - read and write func\n"]}, {"cell_type": "code", "execution_count": 2, "id": "992e1910", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello how are you \n", "i am good \n", "<PERSON><PERSON><PERSON><PERSON> is my name \n", "Welcome to this example\n"]}], "source": ["### Read A Whole file    \n", "with open (\"example.txt\",\"r\") as file:\n", "    content =  file.read()\n", "    print(content)"]}, {"cell_type": "markdown", "id": "2dac4e65", "metadata": {}, "source": ["### "]}, {"cell_type": "code", "execution_count": 1, "id": "230493ae", "metadata": {}, "outputs": [], "source": ["###Binary fils\n", "data = b'\\x00\\x01\\x02\\x03'\n", "with open('example.bin','wb')as file:\n", "    file.write(data)"]}, {"cell_type": "code", "execution_count": null, "id": "fdc29698", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}