{"cells": [{"cell_type": "markdown", "id": "37622f67", "metadata": {}, "source": ["### Operator Overloading\n", "Operator overloading is a feature in Python that allows developers to define how operators, such as +, -, *, /, etc., behave when applied to objects of custom classes."]}, {"cell_type": "code", "execution_count": 1, "id": "20337d68", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nOperator Overloading\\n__add__(self,other): Adds two objects together\\n__sub__(self,other): Subtracts one object from another\\n__mul__(self,other): Multiplies two objects together\\n__truediv__(self,other): Divides one object by another\\n__floordiv__(self,other): Divides one object by another and rounds down to the nearest integer\\n__mod__(self,other): Returns the remainder of one object divided by another\\n__pow__(self,other): Raises one object to the power of another\\n__lt__(self,other): Returns True if one object is less than another\\n__eq__(self,other): Returns True if one object is equal to another\\n'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["###\n", "\"\"\"\n", "Operator Overloading\n", "__add__(self,other): Adds two objects together\n", "__sub__(self,other): Subtracts one object from another\n", "__mul__(self,other): Multiplies two objects together\n", "__truediv__(self,other): Divides one object by another\n", "__floordiv__(self,other): Divides one object by another and rounds down to the nearest integer\n", "__mod__(self,other): Returns the remainder of one object divided by another\n", "__pow__(self,other): Raises one object to the power of another\n", "__lt__(self,other): Returns True if one object is less than another\n", "__eq__(self,other): Returns True if one object is equal to another\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 2, "id": "ab55c1d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vector(6,8)\n"]}], "source": ["class Vector:\n", "    def __init__(self, x, y):\n", "        self.x = x\n", "        self.y = y\n", "    \n", "    def __add__(self, other):\n", "        return Vector(self.x + other.x, self.y + other.y)\n", "    \n", "    def __sub__(self, other):\n", "        return Vector(self.x - other.x, self.y - other.y)\n", "    \n", "    def __mul__(self, other):\n", "        return Vector(self.x * other.x, self.y * other.y)\n", "    \n", "    def __truediv__(self, other):\n", "        return Vector(self.x / other.x, self.y / other.y)\n", "\n", "    def __eq__(self, other):\n", "        return self.x == other.x and self.y == other.y\n", "    \n", "    def __repr__(self):\n", "        return f\"Vector({self.x},{self.y})\"\n", "    \n", "    # creating objects of vector class\n", "\n", "v1 = Vector(2,3)\n", "v2 = Vector(4,5)\n", "print(v1+v2)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "3d8f9b83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Complex<PERSON>umber(3,7)\n", "ComplexNumber(1,-1)\n", "ComplexNumber(-10,11)\n"]}], "source": ["class ComplexNumber:\n", "    def __init__(self, real, imag):\n", "        self.real = real\n", "        self.imag = imag\n", "    \n", "    def __add__(self, other):\n", "        return ComplexNumber(self.real + other.real, self.imag + other.imag)\n", "    \n", "    def __sub__(self, other):\n", "        return ComplexNumber(self.real - other.real, self.imag - other.imag)\n", "    \n", "    def __mul__(self, other):\n", "        real = self.real * other.real - self.imag * other.imag\n", "        imag = self.real * other.imag + self.imag * other.real\n", "        return ComplexNumber(real, imag)\n", "    \n", "    def __truediv__(self, other):\n", "        real = (self.real * other.real + self.imag * other.imag) / (other.real ** 2 + other.imag ** 2)\n", "        imag = (self.imag * other.real - self.real * other.imag) / (other.real ** 2 + other.imag ** 2)\n", "        return ComplexNumber(real, imag)\n", "    \n", "    def __eq__(self, other):\n", "        return self.real == other.real and self.imag == other.imag\n", "    \n", "    def __repr__(self):\n", "        return f\"ComplexNumber({self.real},{self.imag})\"\n", "    \n", "\n", "c1 = ComplexNumber(2,3)\n", "c2 = ComplexNumber(1,4)\n", "print(c1+c2)\n", "print(c1-c2)\n", "print(c1*c2)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "815b589a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}