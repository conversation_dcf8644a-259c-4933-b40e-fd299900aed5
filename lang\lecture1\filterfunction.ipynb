{"cells": [{"cell_type": "code", "execution_count": 3, "id": "cec1ce39", "metadata": {}, "outputs": [], "source": ["def even(num):\n", "    if num%2==0:\n", "        return True"]}, {"cell_type": "code", "execution_count": 4, "id": "fdbbfaab", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["even(24)"]}, {"cell_type": "code", "execution_count": 6, "id": "c73cb15e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 4, 6, 8, 10, 12]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["lst=[1,2,3,4,5,6,7,8,9,10,11,12]\n", "\n", "list(filter(even,lst))"]}, {"cell_type": "code", "execution_count": 9, "id": "becb4c89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[6, 7, 8, 9]\n"]}], "source": ["#filter with lambda funtion\n", "numbers =[1,2,3,4,5,6,7,8,9]\n", "greater_than_five= list(filter(lambda x: x>5, numbers))\n", "print(greater_than_five)"]}, {"cell_type": "code", "execution_count": 11, "id": "5ea1d5de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[6, 8]\n"]}], "source": ["even_Greter_5=list(filter(lambda x: x>5 and x%2==0, numbers))\n", "print(even_Greter_5)"]}, {"cell_type": "code", "execution_count": 13, "id": "0e9b2b47", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': '<PERSON><PERSON>', 'age': 32}, {'name': 'jack', 'age': 33}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["#filter() to check if the age is h=greater than 25 in dictionary\n", "people = [\n", "    {'name':'<PERSON><PERSON>','age':32},\n", "    {'name':'jack','age':33},\n", "    {'name':'<PERSON>','age':25}\n", "]\n", "\n", "def age_greater_than_25(person):\n", "    return person[\"age\"]>25\n", "\n", "list(filter(age_greater_than_25,people))"]}, {"cell_type": "code", "execution_count": null, "id": "52d8dc32", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}