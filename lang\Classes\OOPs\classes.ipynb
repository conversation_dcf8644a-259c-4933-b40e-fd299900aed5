{"cells": [{"cell_type": "markdown", "id": "50ae5c66", "metadata": {}, "source": ["### Classes and Objects\n"]}, {"cell_type": "code", "execution_count": null, "id": "7ff66013", "metadata": {}, "outputs": [], "source": ["class Car:\n", "    pass\n", "audi = Car()        ##Object from class\n", "bmw =Car()"]}, {"cell_type": "code", "execution_count": null, "id": "a704f447", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class '__main__.Car'>\n"]}], "source": ["print(type(audi))   "]}, {"cell_type": "code", "execution_count": 4, "id": "72542e22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Car object at 0x0000014357E6C050>\n", "<__main__.Car object at 0x0000014357C72D50>\n"]}], "source": ["print(audi)\n", "print(bmw)"]}, {"cell_type": "code", "execution_count": null, "id": "de07c8b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Dog object at 0x0000024A09F2DA90>\n", "kittu 3\n"]}], "source": ["##Intance variable and method\n", "class Dog:\n", "    ##Constructor\n", "    def __init__(self,name,age):\n", "        self.name = name \n", "        self.age = age \n", "\n", "##create object\n", "dog1 = Dog(\"buddy\",3)\n", "print(dog1)\n", "print(dog1.name,dog1.a)"]}, {"cell_type": "code", "execution_count": 6, "id": "5762fd90", "metadata": {}, "outputs": [], "source": ["## Define a class with instance Method\n", "class Dog:\n", "    def __init__(self,name,age):\n", "        self.name = name\n", "        self.age =age\n", "    def bark(self):\n", "        print(f\"{self.name} says whoof\")\n", "    "]}, {"cell_type": "code", "execution_count": 8, "id": "a905ffe0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> says whoof\n"]}], "source": ["dog1 = Dog(\"kittu\",2)\n", "dog1.bark()\n"]}, {"cell_type": "code", "execution_count": 9, "id": "899d4f14", "metadata": {}, "outputs": [], "source": ["### Modeling a bank account \n", "## Define  a class for bank account\n", "class BankAccount():\n", "    def __init__(self,owner,balance=0):\n", "        self.owner = owner\n", "        self.balance = balance\n", "    \n", "    def deposit(self,amount):\n", "        self.balance+=amount\n", "        print(f\"{amount} is deposited new balance is {self.balance}\")\n", "\n", "    def withdraw(self, amount):\n", "        if amount > self.balance:\n", "            print(f\"you dont have enough money. your balance is {self.balance}\")\n", "        else:\n", "            self.balance-=amount\n", "            print(f\"{amount} is deducted from you account. new balance is {self.balance}\")\n", "\n", "    def get_balance(self):\n", "        return self.balance"]}, {"cell_type": "code", "execution_count": 12, "id": "********", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100 is deposited new balance is 2100\n"]}], "source": ["##Create an account\n", "account1 = BankAccount(\"kt\",2000)\n", "\n", "## call instance method\n", "account1.deposit(100)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "0a438a75", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["300 is deducted from you account. new balance is 1800\n"]}], "source": ["account1.withdraw(300)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "7286137a", "metadata": {}, "outputs": [{"data": {"text/plain": ["1800"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["account1.get_balance()"]}, {"cell_type": "code", "execution_count": null, "id": "b4313b7a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}