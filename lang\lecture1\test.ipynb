{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f63d6d36", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["1+1"]}, {"cell_type": "code", "execution_count": null, "id": "3fe6a8d9", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'Python' requires the ipykernel package.\n", "\u001b[1;31m<a href='command:jupyter.createPythonEnvAndSelectController'>Create a Python Environment</a> with the required packages.\n", "\u001b[1;31mOr install 'ipykernel' using the command: 'd:/GenAi/langchain/venv/Scripts/python.exe -m pip install ipykernel -U --user --force-reinstall'"]}], "source": ["x=6;y=7;x+y\n"]}, {"cell_type": "markdown", "id": "a1eba5f1", "metadata": {}, "source": ["### Python Example"]}, {"cell_type": "code", "execution_count": 4, "id": "c69432f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 4, 6, 8]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["[num for num in range(10) if num % 2 == 0]"]}, {"cell_type": "code", "execution_count": 5, "id": "5a330722", "metadata": {}, "outputs": [], "source": ["l1 = [1, 2, 3]\n", "l2 = [4, 5, 6]\n", "pair = [(x,y) for x in l1 for y in l2 ]"]}, {"cell_type": "code", "execution_count": 7, "id": "340c219b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n"]}], "source": ["print(pair[1][1])"]}, {"cell_type": "code", "execution_count": 8, "id": "85ba68bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5, 5, 6, 5, 2, 5]\n"]}], "source": ["words = ['hello', 'world', 'python', 'rocks', 'is', 'great']\n", "lengths=[len(words) for words in words]\n", "print(lengths)"]}, {"cell_type": "markdown", "id": "f96b1c92", "metadata": {}, "source": ["### Creating a set"]}, {"cell_type": "code", "execution_count": 12, "id": "a0515d01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5} <class 'set'>\n"]}], "source": ["set = {1, 2, 3, 4, 5}\n", "print(set,type(set))"]}, {"cell_type": "markdown", "id": "d4d7d111", "metadata": {}, "source": ["# it doest allow dupilcate elements"]}, {"cell_type": "markdown", "id": "d60d47f4", "metadata": {}, "source": [" ### Shadow copy\n", " "]}, {"cell_type": "code", "execution_count": 1, "id": "3fcf9224", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3] [1, 2, 3]\n", "[1, 2, 3, 4] [1, 2, 3, 4]\n"]}], "source": ["l1 = [1, 2, 3]\n", "l2 =l1\n", "print(l1,l2)\n", "l1.append(4)\n", "print(l1,l2)"]}, {"cell_type": "code", "execution_count": 2, "id": "832cffaa", "metadata": {}, "outputs": [], "source": ["l3 = l1.copy()"]}, {"cell_type": "code", "execution_count": 3, "id": "85d77af1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3]\n", "[1, 2, 3, 4]\n"]}], "source": ["l1 = [1, 2, 3]\n", "print(l1)\n", "print(l3)"]}, {"cell_type": "markdown", "id": "1a849066", "metadata": {}, "source": ["## Regreasion\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ed541b13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["120\n"]}], "source": ["def factorial(n):\n", "    if n==0:\n", "        return 1\n", "    else:\n", "       return n*factorial(n-1)\n", "print(factorial(5))"]}, {"cell_type": "markdown", "id": "65190809", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "3288eb80", "metadata": {}, "source": ["### Function to read file and count frequency\n"]}, {"cell_type": "code", "execution_count": 7, "id": "3314f84a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'hi': 1, 'my': 1, 'name': 1, 'is': 1, 'kaustubh': 1, 'how': 1, 'are': 2, 'you': 2, 'doing': 1, 'hope': 1, 'fine': 1}\n"]}], "source": ["def word_count_frequency(filepath):\n", "    word_count={}\n", "    with open (filepath,\"r\") as file:\n", "        for line in file:\n", "            words =line.split()\n", "            for word in words:\n", "                word = word.lower().strip('.,!?\";:')\n", "                word_count[word] = word_count.get(word, 0) + 1\n", "    return word_count\n", "\n", "filepath = 'sample.txt'\n", "word_count = word_count_frequency(filepath)\n", "print(word_count)"]}, {"cell_type": "markdown", "id": "78f5f967", "metadata": {}, "source": ["### Validate Email address\n"]}, {"cell_type": "code", "execution_count": 7, "id": "40b48c0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "True\n"]}], "source": ["import re\n", "\n", "def is_valid_email(email):\n", "    \"\"\"This function checks if the provided email address is valid.\"\"\"\n", "    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'\n", "    return re.match(pattern, email) is not None\n", "\n", "#calling the function\n", "print(is_valid_email(\"<EMAIL>\"))\n", "print(is_valid_email(\"<EMAIL>\"))  # Invalid email"]}, {"cell_type": "code", "execution_count": null, "id": "d9de3b6c", "metadata": {}, "outputs": [], "source": [" "]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}