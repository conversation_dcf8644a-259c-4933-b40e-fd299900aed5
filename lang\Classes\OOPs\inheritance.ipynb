{"cells": [{"cell_type": "markdown", "id": "0c1f26f4", "metadata": {}, "source": ["### Inheritance"]}, {"cell_type": "code", "execution_count": 12, "id": "fc59de81", "metadata": {}, "outputs": [], "source": ["## Inheritance\n", "## parent class\n", "class Car:\n", "    def __init__(self,windows,doors,enginetype):\n", "        self.windows = windows\n", "        self.doors =doors \n", "        self.enginetype =enginetype\n", "    def drive(self):\n", "        print(f\"The person will drive the {self.enginetype} car\")\n", "    \n"]}, {"cell_type": "code", "execution_count": 13, "id": "f8c28170", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The person will drive the petrol car\n"]}], "source": ["car1 = Car(4,4,\"petrol\")\n", "car1.drive()"]}, {"cell_type": "code", "execution_count": 16, "id": "c8c46e43", "metadata": {}, "outputs": [], "source": ["##Inherit class\n", "class Tesla(Car):\n", "    def __init__(self,windows,doors,enginetype,is_selfdriving):\n", "        super().__init__(windows,doors,enginetype)\n", "        self.is_selfdriving = is_selfdriving\n", "    def selfdriving(self):\n", "        print(f\"Tesla supports self Driving{self.is_selfdriving}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "06294ea3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tesla supports self DrivingTrue\n"]}], "source": ["tesla1=Tesla(4,5,\"Electric\",True)\n", "tesla1.selfdriving()"]}, {"cell_type": "code", "execution_count": 7, "id": "0d1cc177", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Buddy says woof!\n", "<PERSON>\n"]}], "source": ["#multiple inheritance\n", "\n", "##Base class one\n", "class Animal:\n", "    def __init__(self,name):\n", "        self.name = name\n", "\n", "    def speak(self):\n", "        print(f\"Subclasses must impleament this Method\")\n", "\n", "##Base class two\n", "class Pet:\n", "    def __init__(self,owner):\n", "        self.owner = owner\n", "\n", "##Derived Class\n", "class Dog(Animal,Pet):\n", "    def __init__(self,name,owner):\n", "        Animal.__init__(self,name)\n", "        Pet.__init__(self,owner)\n", "\n", "    def speak(self):\n", "        return f\"{self.name} says woof!\"\n", "    \n", "## Object\n", "dog1 = Dog(\"<PERSON>\",\"<PERSON>\")\n", "print(dog1.speak())\n", "print(dog1.owner)\n"]}, {"cell_type": "code", "execution_count": null, "id": "663d7829", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}