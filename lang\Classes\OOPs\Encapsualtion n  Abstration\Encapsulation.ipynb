{"cells": [{"cell_type": "markdown", "id": "9efeb439", "metadata": {}, "source": ["### Encapsulation and Abstaction\n", "\n"]}, {"cell_type": "markdown", "id": "6857a041", "metadata": {}, "source": ["#### Encapsulation\n", "encapsulation is the concept of wrapping date(variables) and methods(functions) together into a single unit. It restricts direct access to some of the object's components, which is a mean of preventing accidental interferense and misuse of it.\n"]}, {"cell_type": "code", "execution_count": null, "id": "74c1f1bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>\n", "30\n", "10\n"]}, {"data": {"text/plain": ["'John'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["### Encapsulation with getter and setter methods\n", "## Public, Protected and Private va irable\n", "\n", "class Person:\n", "    def __init__(self, name, age, height):\n", "        self.name = name    # public variable\n", "        self.age = age    # private variable\n", "        self.height = height  # protected variable\n", "\n", "def get_name(Person):\n", "    return Person.name\n", "\n", "person=Person(\"<PERSON>\", 30,height= 10)\n", "print(person.name)\n", "print(person.age)\n", "print(person.height)\n", "\n", "get_name(person)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "3e131ab9", "metadata": {}, "outputs": [{"data": {"text/plain": ["['__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__firstlineno__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getstate__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__static_attributes__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " 'age',\n", " 'height',\n", " 'name']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(person) "]}, {"cell_type": "code", "execution_count": 14, "id": "499540ff", "metadata": {}, "outputs": [], "source": ["class Person:\n", "    def __init__(self, name, age, height):\n", "        self.__name = name  # private variable  \n", "        self.__age = age    # private variable \n", "        self.height = height  # protected variable"]}, {"cell_type": "code", "execution_count": 21, "id": "e8aa6777", "metadata": {}, "outputs": [{"data": {"text/plain": ["'John'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["person = Person(\"<PERSON>\", 30,10)\n", "dir(person)\n", "\n", "def get_name(Person):\n", "    return Person._Person__name\n", "\n", "person = Person(\"<PERSON>\", 30,10)\n", "get_name(person)\n"]}, {"cell_type": "code", "execution_count": null, "id": "df2ee422", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>\n"]}], "source": ["## Protected\n", "class Person:\n", "    def __init__(self, name, age, height):\n", "        self._name = name  # protected variable  \n", "        self._age = age    # protected variable \n", "        self._height = height  # protected variable\n", "class Employee(Person):\n", "    def __init__(self, name, age, height, salary):\n", "        super().__init__(name, age, height)\n", "        \n", "\n", "employee = Employee(\"<PERSON>\", 30,10,1000)\n", "print(employee._name)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "acb8ea82", "metadata": {}, "outputs": [{"data": {"text/plain": ["'John'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["## Encapsulation with getter and setter methods\n", "class Person:\n", "    def __init__(self, name, age, height):\n", "        self.__name = name  # private variable  \n", "        self.__age = age    # private variable \n", "        self.__height = height  # private variable\n", "    def get_name(self):\n", "        return self.__name\n", "  \n", "    def set_name(self, name):\n", "        self.__name = name\n", "\n", "person = Person(\"<PERSON>\", 30,10)\n", "person.get_name()\n", "\n"]}, {"cell_type": "code", "execution_count": 39, "id": "81eaa8d0", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Jane'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["person.set_name(\"<PERSON>\")\n", "person.get_name()"]}, {"cell_type": "code", "execution_count": null, "id": "0e39fe4f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec4c27e0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}