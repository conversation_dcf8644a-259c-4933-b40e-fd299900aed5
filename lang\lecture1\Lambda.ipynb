{"cells": [{"cell_type": "markdown", "id": "5c4b09d1", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "369fa06b", "metadata": {}, "outputs": [], "source": ["#syntax\n", "lambda argument: expression"]}, {"cell_type": "code", "execution_count": 1, "id": "9de20410", "metadata": {}, "outputs": [], "source": ["def addition(x, y):\n", "    return x + y"]}, {"cell_type": "code", "execution_count": 2, "id": "ed0a863f", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["addition(2,3)"]}, {"cell_type": "code", "execution_count": 3, "id": "5748caf6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'function'>\n"]}], "source": ["addition = lambda a,b: a + b\n", "print(type(addition))"]}, {"cell_type": "code", "execution_count": 5, "id": "6fa206a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["even = lambda num: num%2==0\n", "\n", "print(even(4))  # True\n", "print(even(5))  # False"]}, {"cell_type": "code", "execution_count": 6, "id": "baa180bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<map at 0x24e3b87a050>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["number = [1,2,3,4,5,6]\n", "map(lambda x:x**2,number)"]}, {"cell_type": "markdown", "id": "14ad3152", "metadata": {}, "source": ["map use lazy loading technique"]}, {"cell_type": "code", "execution_count": 7, "id": "2d6a5785", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25, 36]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["list(map(lambda x: x**2, number))"]}, {"cell_type": "markdown", "id": "80cba6c4", "metadata": {}, "source": ["# Map function\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4d6b00c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["def square(x):\n", "    return x**2\n", "square(10)"]}, {"cell_type": "code", "execution_count": 12, "id": "ca3d3b5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<map object at 0x0000024E3B8B82E0>\n", "[1, 4, 9, 16, 25, 36, 49, 64, 81]\n"]}], "source": ["numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "maped=map(square, numbers)\n", "print(maped)\n", "print(list(maped))"]}, {"cell_type": "code", "execution_count": 13, "id": "6282ba5d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 4, 9, 16, 25, 36, 49, 64, 81]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Using a lambda functioj with map\n", "numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "list(map(lambda x: x**2, numbers))"]}, {"cell_type": "code", "execution_count": 15, "id": "213b2fcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[11, 22, 33, 44, 55]\n"]}], "source": ["### Map multiple iterables\n", "numbers1 = [1, 2, 3, 4, 5]\n", "numbers2 = [10, 20, 30, 40, 50]\n", "added_number=list(map(lambda x, y: x + y, numbers1, numbers2))\n", "print(added_number)  # [11, 22, 33, 44, 55]"]}, {"cell_type": "code", "execution_count": 16, "id": "0ef9a6bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5]\n"]}], "source": ["# map to convert a list strings to integers\n", "string_numbers = ['1', '2', '3', '4', '5']\n", "int_numbers = list(map(int, string_numbers))\n", "print(int_numbers)  # [1, 2, 3, 4,"]}, {"cell_type": "code", "execution_count": 18, "id": "324f5851", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['HELLO', 'WORLD', 'PYTHON']\n"]}], "source": ["words = ['hello', 'world', 'python']\n", "uppercase_words = list(map(str.upper, words))\n", "print(uppercase_words)  # ['HELLO', 'WORLD', 'PYTHON']"]}, {"cell_type": "code", "execution_count": 20, "id": "d040bcb4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON>', '<PERSON>', '<PERSON>']\n"]}], "source": ["def get_name(person):\n", "    return person['name']\n", "\n", "people = [\n", "    {'name': '<PERSON>', 'age': 30},\n", "    {'name': '<PERSON>', 'age': 25},\n", "    {'name': '<PERSON>', 'age': 35}\n", "]\n", "list_of_names = list(map(get_name, people))\n", "print(list_of_names)  # ['<PERSON>', '<PERSON>', '<PERSON>']"]}, {"cell_type": "code", "execution_count": null, "id": "6601fec2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}