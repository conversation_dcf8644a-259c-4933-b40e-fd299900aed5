{"cells": [{"cell_type": "markdown", "id": "7e9279ee", "metadata": {}, "source": ["### Magic Methods\n", "Magic method known as dunder method(Double Underscore methods), are special methods that start and end with double underscores.these methods enables you to define the behavior of objects for buids in operations, such as arthmetic, comparison, and string representation."]}, {"cell_type": "markdown", "id": "2a15f3ac", "metadata": {}, "source": ["#### magic methods\n", "Magic method are predined methods in python that can override to change the behavior of objects."]}, {"cell_type": "code", "execution_count": 1, "id": "1761f493", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n- __init__: Initilizes a new instance of a class.\\n- __str__: Returns a string representation of an object.\\n- __repr__: Returns an official string representation of an object.\\n- __len__: Returns the length of an object.\\n- __getitem__: Returns an item from an object.\\n- __setitem__: Sets an item in an object.\\n- __delitem__: Deletes an item from an object.\\n- __iter__: Returns an iterator for an object.\\n'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "- __init__: Initilizes a new instance of a class.\n", "- __str__: Returns a string representation of an object.\n", "- __repr__: Returns an official string representation of an object.\n", "- __len__: Returns the length of an object.\n", "- __getitem__: Returns an item from an object.\n", "- __setitem__: Sets an item in an object.\n", "- __delitem__: Deletes an item from an object.\n", "- __iter__: Returns an iterator for an object.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 2, "id": "25ef251a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Person object at 0x000002320D5ED010>\n"]}], "source": ["class Person:\n", "    pass\n", "\n", "person = Person()\n", "print(person)"]}, {"cell_type": "code", "execution_count": 3, "id": "74e44055", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Person object at 0x000002320D5ED400>\n"]}], "source": ["## Basic methods\n", "class Person:\n", "    def __init__(self, name , age):\n", "        self.name = name\n", "        self.age = age\n", "\n", "person = Person(\"Kt\",24)\n", "print(person)"]}, {"cell_type": "code", "execution_count": 8, "id": "3771836d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> <PERSON><PERSON>, 24 years old\n", "Person(name=Kt, age=24)\n"]}], "source": ["## Basic methods\n", "class Person:\n", "    def __init__(self, name , age):\n", "        self.name = name\n", "        self.age = age\n", "\n", "    def __str__(self):\n", "        return f\"Person {self.name}, {self.age} years old\"\n", "\n", "    def __repr__(self):\n", "        return f\"Person(name={self.name}, age={self.age})\"\n", "\n", "person = Person(\"Kt\",24)\n", "print(person)\n", "print(repr(person))"]}, {"cell_type": "code", "execution_count": null, "id": "18e652d1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}